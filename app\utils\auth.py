from functools import wraps
from flask import request, session, redirect, url_for, flash, abort


def check_cookie_auth():
    """Simplified authentication check using fingerprinting"""
    # Allow certain paths without authentication
    allowed_paths = ['/gate', '/static', '/uploads', '/fp/']
    if any(request.path.startswith(path) for path in allowed_paths) or request.path == '/':
        return

    # Check if user is logged in via session (admin/publisher access)
    if session.get('user_id'):
        return  # User is logged in, allow access

    # Check for new fingerprint system
    fingerprint = request.cookies.get('fingerprint')

    # If no fingerprint cookie, redirect to gate for setup
    if not fingerprint:
        return redirect_to_gate_with_return_url()

    # Verify fingerprint exists in database
    try:
        from app.fp import FingerprintManager
        fp_data = FingerprintManager.get_fingerprint_data(fingerprint)
        if not fp_data:
            return redirect_to_gate_with_return_url()
    except Exception as e:
        return redirect_to_gate_with_return_url()


def redirect_to_gate_with_return_url():
    """Redirect to gate with the current URL as return parameter"""
    from urllib.parse import quote, urlparse

    # Get the current URL that user was trying to access
    return_url = request.url

    # Extract just the path from the URL (remove domain, protocol, etc.)
    try:
        parsed_url = urlparse(return_url)
        return_path = parsed_url.path

        # Include query string if it exists and is safe
        if parsed_url.query:
            # Only include safe query parameters
            safe_query = sanitize_query_string(parsed_url.query)
            if safe_query:
                return_path += '?' + safe_query
    except:
        return_path = '/'

    # Validate the return path for security
    safe_return_path = validate_internal_redirect_path(return_path)

    # URL encode the return path to safely pass it as a parameter
    encoded_return_url = quote(safe_return_path, safe='/')

    # Redirect to gate with return URL parameter
    return redirect(f'/gate?next={encoded_return_url}')


def require_admin(f):
    """Decorator to require admin authentication"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('user_id'):
            flash('Please log in to access this page.', 'error')
            return redirect(url_for('auth.login'))

        # Check if user is admin (you may need to adjust this based on your user model)
        user_role = session.get('role', 'user')
        if user_role != 'admin':
            abort(403, 'Admin access required')

        return f(*args, **kwargs)
    return decorated_function


def validate_internal_redirect_path(path):
    """
    Validate internal redirect path for security.
    Only allows paths that are generated by the application itself.
    """
    if not path or path == '/':
        return '/'

    # Remove any leading/trailing whitespace
    path = path.strip()

    # Ensure path starts with /
    if not path.startswith('/'):
        return '/'

    # Define allowed internal routes (whitelist approach)
    allowed_routes = [
        '/app/',           # App detail pages
        '/category/',      # Category pages
        '/search',         # Search pages
        '/suggestions',    # Suggestions page
        '/admin/',         # Admin pages
        '/publisher/',     # Publisher pages
        '/auth/',          # Auth pages
    ]

    # Check if path starts with any allowed route
    is_allowed = any(path.startswith(route) for route in allowed_routes)

    # Also allow exact matches for specific pages
    exact_allowed = ['/', '/about', '/contact', '/privacy', '/terms']
    if path in exact_allowed:
        is_allowed = True

    # If not in whitelist, redirect to home
    if not is_allowed:
        return '/'

    # Additional security checks
    if len(path) > 200:  # Limit URL length
        return '/'

    # Block suspicious patterns
    suspicious_patterns = ['../', '..\\', '%2e%2e', 'javascript:', 'data:', 'vbscript:']
    path_lower = path.lower()
    for pattern in suspicious_patterns:
        if pattern in path_lower:
            return '/'

    return path


def sanitize_query_string(query_string):
    """
    Sanitize query string to only allow safe parameters.
    """
    if not query_string:
        return ''

    # Define allowed query parameters
    allowed_params = ['page', 'category', 'search', 'sort', 'order', 'limit']

    try:
        from urllib.parse import parse_qs, urlencode

        # Parse query string
        params = parse_qs(query_string, keep_blank_values=False)

        # Filter to only allowed parameters
        safe_params = {}
        for key, values in params.items():
            if key in allowed_params and values:
                # Take only the first value and sanitize it
                value = str(values[0])[:100]  # Limit length
                # Remove potentially dangerous characters
                if not any(char in value for char in ['<', '>', '"', "'", '&', ';', '(', ')']):
                    safe_params[key] = value

        # Rebuild query string
        if safe_params:
            return urlencode(safe_params)
    except:
        pass

    return ''


def require_admin(f):
    """Decorator to require admin access"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('user_id') or session.get('role') != 'admin':
            flash('Access denied - Admin privileges required', 'error')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function


def require_login(f):
    """Decorator to require user login"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('user_id'):
            flash('Please log in to access this page', 'error')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function


def require_admin(f):
    """Decorator to require admin authentication"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('user_id'):
            flash('Please log in to access this page.', 'error')
            return redirect(url_for('auth.login'))

        user_role = session.get('role', 'user')
        if user_role != 'admin':
            abort(403, 'Admin access required')

        return f(*args, **kwargs)
    return decorated_function


def require_publisher_or_admin(f):
    """Decorator to require publisher or admin access"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('user_id'):
            flash('Please log in to access this page', 'error')
            return redirect(url_for('auth.login'))

        user_role = session.get('role')
        if user_role not in ['publisher', 'admin']:
            flash('Access denied - Publisher or Admin privileges required', 'error')
            return redirect(url_for('main.index'))
        return f(*args, **kwargs)
    return decorated_function
